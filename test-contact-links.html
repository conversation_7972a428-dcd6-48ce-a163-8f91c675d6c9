<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار روابط الاتصال - AL-SALAMAT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .contact-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .contact-icon {
            font-size: 24px;
            margin-left: 10px;
        }
        .contact-action-btn {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 5px;
            margin-right: 10px;
            display: inline-block;
        }
        .contact-action-btn:hover {
            background: #0056b3;
        }
        .test-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #1e7e34;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار روابط الاتصال</h1>
        
        <div class="test-section">
            <h2>📞 روابط الاتصال الحالية</h2>
            
            <div class="contact-item">
                <div class="contact-icon">📞</div>
                <div>
                    <strong>الهاتف:</strong>
                    <span id="contact-phone-display">######</span>
                    <a href="tel:######" id="contact-phone-link" class="contact-action-btn">اتصل الآن</a>
                </div>
            </div>
            
            <div class="contact-item">
                <div class="contact-icon">📧</div>
                <div>
                    <strong>البريد الإلكتروني:</strong>
                    <span id="contact-email-display">######</span>
                    <a href="mailto:######" id="contact-email-link" class="contact-action-btn">إرسال بريد</a>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 أدوات الاختبار</h2>
            
            <button class="test-btn" onclick="testWithRealData()">اختبار بيانات حقيقية</button>
            <button class="test-btn" onclick="testWithoutData()">اختبار بدون بيانات</button>
            <button class="test-btn" onclick="checkCurrentLinks()">فحص الروابط الحالية</button>
            <button class="test-btn" onclick="clearData()">مسح البيانات</button>
        </div>

        <div class="test-section">
            <h2>📊 نتائج الاختبار</h2>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function updateContactLinks(data) {
            const emailLink = document.getElementById('contact-email-link');
            const phoneLink = document.getElementById('contact-phone-link');
            const emailDisplay = document.getElementById('contact-email-display');
            const phoneDisplay = document.getElementById('contact-phone-display');

            if (data.contactEmail && emailLink && emailDisplay) {
                emailDisplay.textContent = data.contactEmail;
                emailLink.href = `mailto:${data.contactEmail}`;
                emailLink.onclick = null;
                addResult(`✅ تم تحديث رابط البريد الإلكتروني: ${data.contactEmail}`, 'success');
            }

            if (data.contactPhone && phoneLink && phoneDisplay) {
                phoneDisplay.textContent = data.contactPhone;
                phoneLink.href = `tel:${data.contactPhone}`;
                phoneLink.onclick = null;
                addResult(`✅ تم تحديث رابط الهاتف: ${data.contactPhone}`, 'success');
            }
        }

        function setDefaultContactLinks() {
            const emailLink = document.getElementById('contact-email-link');
            const phoneLink = document.getElementById('contact-phone-link');
            const emailDisplay = document.getElementById('contact-email-display');
            const phoneDisplay = document.getElementById('contact-phone-display');

            emailDisplay.textContent = '######';
            phoneDisplay.textContent = '######';

            if (emailLink) {
                emailLink.href = '#';
                emailLink.onclick = function(e) {
                    e.preventDefault();
                    alert('معلومات البريد الإلكتروني غير متاحة حالياً.');
                    addResult('⚠️ تم النقر على رابط البريد المعطل', 'error');
                };
            }

            if (phoneLink) {
                phoneLink.href = '#';
                phoneLink.onclick = function(e) {
                    e.preventDefault();
                    alert('معلومات الهاتف غير متاحة حالياً.');
                    addResult('⚠️ تم النقر على رابط الهاتف المعطل', 'error');
                };
            }

            addResult('🔒 تم تعطيل روابط الاتصال', 'info');
        }

        function testWithRealData() {
            addResult('🧪 اختبار البيانات الحقيقية...', 'info');
            updateContactLinks({
                contactEmail: '<EMAIL>',
                contactPhone: '+966501234567'
            });
            addResult('✅ تم تعيين البيانات الحقيقية - جرب النقر على الأزرار الآن!', 'success');
        }

        function testWithoutData() {
            addResult('🧪 اختبار بدون بيانات...', 'info');
            setDefaultContactLinks();
        }

        function checkCurrentLinks() {
            const emailLink = document.getElementById('contact-email-link');
            const phoneLink = document.getElementById('contact-phone-link');
            
            addResult('🔍 فحص الروابط الحالية:', 'info');
            addResult(`📧 رابط البريد: ${emailLink ? emailLink.href : 'غير موجود'}`, 'info');
            addResult(`📞 رابط الهاتف: ${phoneLink ? phoneLink.href : 'غير موجود'}`, 'info');
        }

        function clearData() {
            document.getElementById('test-results').innerHTML = '';
            addResult('🧹 تم مسح نتائج الاختبار', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 تم تحميل صفحة اختبار روابط الاتصال', 'success');
            addResult('💡 استخدم الأزرار أعلاه لاختبار الروابط', 'info');
            
            // Set default disabled links initially
            setDefaultContactLinks();
        });
    </script>
</body>
</html>
